import { StyleSheet } from 'react-native';
import { useTheme } from 'react-native-paper';

export const useTabButtonStyle = (variant: 'default' | 'chart' = 'default') => {
  const theme = useTheme();

  return StyleSheet.create({
    button: {
      paddingVertical: 8,
      paddingHorizontal: 20,
      borderRadius: 20,
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: 'transparent',
      // Variant-specific styles
      ...(variant === 'chart' && {
        marginHorizontal: 5,
        minWidth: 120,
      }),
    },
    activeButton: {
      borderWidth: 0,
    },
    disabledButton: {
      opacity: 0.5,
    },
    text: {
      fontSize: 16,
      color: theme.colors.primary,
      fontWeight: 'bold',
    },
    activeText: {
      color: '#FFFFFF',
      fontWeight: 'bold',
    },
    disabledText: {
      color: '#CCCCCC',
    },
  });
};
