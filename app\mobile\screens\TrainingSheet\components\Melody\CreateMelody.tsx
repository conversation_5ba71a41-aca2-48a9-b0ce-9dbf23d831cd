import React from 'react';
import {View, ScrollView, TouchableOpacity, Text} from 'react-native';
import {
  Button,
  IconButton,
  Modal,
  Portal,
  TextInput,
  useTheme,
} from 'react-native-paper';
import Slider from '@react-native-community/slider';
import {TopNav} from '../../../../components/TopNav/TopNav';

import PianoKeys from './PianoKeys';
import CreateMelodyStyle from '../../styles/Melody/CreateMelodyStyle';
import {
  MoveRight,
  Pencil,
  Plus,
  RotateCw,
  Save,
  Space,
  Trash,
} from 'lucide-react-native';
import useCreateMelody from '../../hooks/useCreateMelody';

const CreateMelody = () => {
  const {
    navigation,
    melody,
    dispatch,
    showModal,
    setShowModal,
    modalMode,
    setModalMode,
    titleName,
    setTitleName,
    editName,
    setEditName,
    beats,
    pianoRange,
    beatRange,
    scaleDirections,
    changeChord,
    changeNote,
    addNote,
    setBeat,
    setScale,
    changeBeat,
    changeScale,
    saveMelody,
  } = useCreateMelody();
  const theme = useTheme();
  const {styles} = CreateMelodyStyle();

  return (
    <>
      <TopNav
        title={melody.name}
        backFunction={() => navigation.navigate('ExerciseSheet')}>
        <IconButton
          style={{borderWidth: 0}}
          icon={() => <Pencil size={24} color="#FFFFFF" />}
          mode="outlined"
          size={18}
          onPress={() => setEditName(true)}
        />
      </TopNav>

      <View style={styles.container}>
        {/* Chord Notes */}
        <View style={styles.item}>
          <Text style={styles.label}>Chord Notes</Text>
          <View style={styles.row}>
            <IconButton
              icon={() => <RotateCw color="#A0A4A8" strokeWidth={2.5} />}
              onPress={() => dispatch({type: 'CLEAR_CHORD_NOTE'})}
            />

            {melody.chordNotes.map((note, index) => (
              <View style={styles.noteList} key={'chord' + index}>
                <Button
                  compact
                  mode="outlined"
                  textColor="#000"
                  style={[
                    styles.chip,
                    {
                      borderColor:
                        melody.selectedChordNote === index &&
                        melody.changingChord
                          ? '#000'
                          : '#A0A4A8',
                    },
                  ]}
                  contentStyle={styles.noteButtonContent}
                  onPress={() => changeChord(index)}>
                  {note}
                </Button>
                {index < melody.chordNotes.length - 1 && (
                  <View
                    style={{
                      alignContent: 'center',
                      justifyContent: 'center',
                      marginHorizontal: 6,
                    }}>
                    <MoveRight color="#A0A4A8" />
                  </View>
                )}
              </View>
            ))}
          </View>
        </View>

        {/* Beat */}
        <View style={styles.item}>
          <Text style={styles.label}>Beat</Text>
          <Button
            mode="outlined"
            onPress={changeBeat}
            textColor="#000"
            style={styles.button}>
            {melody.beat}
          </Button>
        </View>

        {/* Tempo */}
        <View style={styles.tempoItem}>
          <Text style={styles.label}>Tempo</Text>
          <View style={styles.slider}>
            <Slider
              style={styles.sliderBar}
              minimumValue={40}
              maximumValue={180}
              step={1}
              value={melody.tempo}
              onValueChange={val => dispatch({type: 'SET_TEMPO', payload: val})}
            />
            <Text style={styles.tempoText}>{melody.tempo} BPM</Text>
          </View>
        </View>

        {/* Scale Direction */}
        <View style={styles.item}>
          <Text style={styles.label}>Scale Direction</Text>
          <View style={styles.row}>
            <Button
              mode="outlined"
              textColor="#000"
              onPress={changeScale}
              style={styles.button}>
              {melody.scaleDirection.charAt(0).toUpperCase() +
                melody.scaleDirection.slice(1)}
            </Button>
          </View>
        </View>

        {/* Notes Sequence */}
        <View style={{maxHeight: '40%'}}>
          <View style={{flexDirection: 'row', justifyContent: 'space-between'}}>
            <View style={{flexDirection: 'row', alignItems: 'center'}}>
              <Text style={styles.label}>Notes Sequence</Text>

              <IconButton
                icon={() => (
                  <RotateCw size={20} color="#A0A4A8" strokeWidth={2.5} />
                )}
                style={{width: 24, height: 24}}
                onPress={() => dispatch({type: 'CLEAR_NOTE'})}
              />
            </View>
            <View style={{flexDirection: 'row', alignItems: 'center'}}>
              <IconButton
                icon={() => (
                  <Space size={20} color="#A0A4A8" strokeWidth={2.5} />
                )}
                style={{width: 24, height: 24}}
                onPress={() => addNote('')}
              />
              <IconButton
                icon={() => (
                  <Trash size={20} color="#A0A4A8" strokeWidth={2.5} />
                )}
                style={{width: 24, height: 24}}
                onPress={() =>
                  dispatch({type: 'DELETE_NOTE', index: melody.selectedNote})
                }
              />
            </View>
          </View>

          <View style={{maxHeight: 160, paddingVertical: 10, zIndex: 10}}>
            <ScrollView contentContainerStyle={styles.noteRow}>
              {melody.notes.map((note, index) => (
                <View style={styles.noteList} key={'note' + index}>
                  <Button
                    compact
                    mode="outlined"
                    textColor="#000"
                    style={[
                      styles.noteButton,
                      {
                        borderColor:
                          index === melody.selectedNote && !melody.changingChord
                            ? '#969696'
                            : index % beats[melody.beat] === 0
                            ? '#3C80F3'
                            : '#4FC3C7',

                        backgroundColor:
                          index === melody.selectedNote && !melody.changingChord
                            ? theme.colors.background
                            : index % beats[melody.beat] === 0
                            ? 'rgba(60,128,243,0.3)'
                            : 'rgba(79,195,199,0.05)',
                      },
                    ]}
                    contentStyle={[styles.noteButtonContent]}
                    onPress={() => changeNote(index)}>
                    {note}
                  </Button>

                  <View
                    style={{
                      alignContent: 'center',
                      justifyContent: 'center',
                      marginHorizontal: 6,
                    }}>
                    <MoveRight width={16} color="#A0A4A8" />
                  </View>
                </View>
              ))}

              <TouchableOpacity
                onPress={() => changeNote(-1)}
                style={[
                  styles.noteButton,
                  {
                    backgroundColor:
                      melody.selectedNote === -1 && !melody.changingChord
                        ? '#4FC3C7'
                        : theme.colors.primary,
                    borderColor:
                      melody.selectedNote === -1 && !melody.changingChord
                        ? '#4FC3C7'
                        : theme.colors.primary,
                  },
                ]}
                activeOpacity={1}>
                <Plus size={28} color="white" />
              </TouchableOpacity>
            </ScrollView>
          </View>
        </View>

        {/* Piano Keys */}
        <View style={styles.piano}>
          <IconButton
            mode="contained-tonal"
            style={{
              backgroundColor: theme.colors.primary,
              marginBottom: 10,
            }}
            size={35}
            icon={() => <Save size={35} color="white" />}
            onPress={saveMelody}
          />
          <ScrollView horizontal={true}>
            {pianoRange.map(octave => {
              return (
                <PianoKeys key={octave} octave={octave} addNote={addNote} />
              );
            })}
          </ScrollView>
        </View>

        <Portal>
          <Modal
            visible={editName}
            onDismiss={() => setEditName(false)}
            style={styles.modal}>
            <View style={styles.modalItem}>
              <TextInput
                value={titleName}
                onChangeText={text => setTitleName(text)}
                mode="outlined"
                style={{width: '100%'}}
              />

              <View
                style={{
                  flexDirection: 'row',
                  justifyContent: 'space-between',
                  paddingTop: 10,
                  width: '100%',
                }}>
                <Button
                  mode="contained"
                  style={styles.editModalButtons}
                  onPress={() => {
                    dispatch({type: 'SET_NAME', payload: titleName});
                    setEditName(false);
                  }}>
                  <Text>Confirm</Text>
                </Button>
                <Button
                  mode="outlined"
                  style={styles.editModalButtons}
                  onPress={() => {
                    setTitleName('');
                    setEditName(false);
                  }}>
                  <Text>Cancel</Text>
                </Button>
              </View>
            </View>
          </Modal>
        </Portal>

        <Portal>
          <Modal
            visible={showModal}
            onDismiss={() => setShowModal(false)}
            style={styles.modal}>
            <View style={styles.modalItem}>
              {modalMode === 'beat'
                ? beatRange.map(oneBeat => {
                    return (
                      <Button
                        key={oneBeat}
                        style={[
                          styles.modalButton,
                          {
                            backgroundColor:
                              oneBeat === melody.beat
                                ? theme.colors.primary
                                : 'transparent',
                          },
                        ]}
                        textColor={oneBeat === melody.beat ? 'white' : '#000'}
                        onPress={() => setBeat(oneBeat)}>
                        {oneBeat}
                      </Button>
                    );
                  })
                : scaleDirections.map(scale => {
                    return (
                      <Button
                        key={scale}
                        style={[
                          styles.modalButton,
                          {
                            backgroundColor:
                              scale === melody.scaleDirection
                                ? theme.colors.primary
                                : 'transparent',
                          },
                        ]}
                        textColor={
                          scale === melody.scaleDirection ? 'white' : '#000'
                        }
                        onPress={() => setScale(scale)}>
                        {scale.charAt(0).toUpperCase() + scale.slice(1)}
                      </Button>
                    );
                  })}
            </View>
          </Modal>
        </Portal>
      </View>
    </>
  );
};

export default CreateMelody;
