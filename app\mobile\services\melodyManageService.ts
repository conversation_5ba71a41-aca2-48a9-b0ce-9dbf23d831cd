import {Dispatch, SetStateAction} from 'react';
import {openDatabase, SQLiteDatabase} from 'react-native-sqlite-storage';

export type scaleDirectionTypes = 'ascending' | 'descending' | 'none';

export type Melody = {
  name: string;
  chord: string[];
  beat: string;
  tempo: number;
  scale: scaleDirectionTypes;
  notes: string[];
};

export const openMelodyDatabase = () => {
  return openDatabase({
    name: 'Melody.db',
    location: 'default',
  });
};

export const createMelodyTable = async (db: SQLiteDatabase) => {
  try {
    await db.executeSql(
      `CREATE TABLE IF NOT EXISTS Melody (
        melody_id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        chord TEXT NOT NULL,
        beat TEXT NOT NULL,
        tempo INTEGER NOT NULL,
        scale TEXT NOT NULL,
        notes TEXT NOT NULL
      );`,
    );

    console.log('Melody table created successfully');
  } catch (error) {
    console.error('Error creating table:', error);
  }
};

export const insertMelody = async (
  db: SQLiteDatabase,
  melody: Melody,
  setMelodies?: Dispatch<SetStateAction<Melody[]>>,
) => {
  try {
    await db.executeSql(
      `INSERT INTO Melody (name, chord, beat, tempo, scale, notes)
     VALUES (?, ?, ?, ?, ?, ?)`,
      [
        melody.name,
        JSON.stringify(melody.chord),
        melody.beat,
        melody.tempo,
        melody.scale,
        JSON.stringify(melody.notes),
      ],
    );

    if (setMelodies) {
      await fetchMelodies(db, setMelodies);
    }
  } catch (error) {
    console.log('Error inserting melody: ', error);
  }
};

export const fetchMelodies = async (
  db: SQLiteDatabase,
  setMelodies: Dispatch<SetStateAction<Melody[]>>,
) => {
  try {
    const results = await db.executeSql(`SELECT * FROM Melody`);
    const melodies: Melody[] = [];

    results.forEach(result => {
      for (let i = 0; i < result.rows.length; i++) {
        const row = result.rows.item(i);
        melodies.push({
          name: row.name,
          chord: JSON.parse(row.chord),
          beat: row.beat,
          tempo: row.tempo,
          scale: row.scale as scaleDirectionTypes,
          notes: JSON.parse(row.notes),
        });
      }
    });

    setMelodies(melodies);

    return melodies;
  } catch (error) {
    console.log('Error fetching melodies: ', error);
  }
};
