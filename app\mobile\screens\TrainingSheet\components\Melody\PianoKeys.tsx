import React from 'react';
import {View, StyleSheet, Text, Pressable} from 'react-native';
import PianoKeysStyle from '../../styles/Melody/PianoKeysStyle';
import getNoteSound from '../../../../utils/pianoNotes.ts';
import Sound from 'react-native-sound';

type Key = {
  note: string;
  isBlack: boolean;
  position: number;
};

const keys: Key[] = [
  {note: 'C', isBlack: false, position: 0},
  {note: 'Cs', isBlack: true, position: 0.7},
  {note: 'D', isBlack: false, position: 1},
  {note: 'Ds', isBlack: true, position: 1.7},
  {note: 'E', isBlack: false, position: 2},
  {note: 'F', isBlack: false, position: 3},
  {note: 'Fs', isBlack: true, position: 3.7},
  {note: 'G', isBlack: false, position: 4},
  {note: 'Gs', isBlack: true, position: 4.7},
  {note: 'A', isBlack: false, position: 5},
  {note: 'As', isBlack: true, position: 5.7},
  {note: 'B', isBlack: false, position: 6},
];

type PianoKeysProps = {
  octave: number;

  addNote: (note: string) => void;
};

const PianoKeys: React.FC<PianoKeysProps> = ({octave, addNote}) => {
  const {styles, WHITE_KEY_WIDTH} = PianoKeysStyle();

  const playNote = (noteFile: any) => {
    const sound = new Sound(noteFile, error => {
      if (error) {
        console.log('Failed to load sound', error);
        return;
      }
      sound.play(() => {
        sound.release();
      });
    });
  };

  const {soundMap} = getNoteSound();
  return (
    <View style={styles.container}>
      {/* White Keys */}
      <View style={styles.whiteKeys}>
        {keys
          .filter(k => !k.isBlack)
          .map(key => (
            <Pressable
              key={key.note + octave}
              onPress={() => {
                console.log("Note played ", key.note + octave)
                playNote(soundMap[key.note + octave]);
                addNote(key.note + octave);
              }}
              style={({pressed}) => [
                styles.whiteKey,
                {
                  zIndex: 1, 
                  backgroundColor: pressed
                    ? 'rgba(0,0,0,0.08)'
                    : 'rgba(0,0,0,0.02)', // pressed color here
                },
              ]}>
             
                <Text style={styles.label}>{key.note + octave}</Text>
             
            </Pressable>
          ))}
      </View>

      {/* Black Keys */}
      <View style={[StyleSheet.absoluteFill, , { pointerEvents: 'box-none' }]}>
        {keys
          .filter(k => k.isBlack)
          .map(key => (
            <Pressable
              key={key.note + octave}
              onPress={() => {
                console.log("Note played ", key.note + octave)
                playNote(soundMap[key.note + octave]);
                addNote(key.note[0] + '#' + octave);
              }}
              style={({pressed}) => [
                styles.blackKey,
                {
                  left: key.position * WHITE_KEY_WIDTH,
                  backgroundColor: pressed ? 'rgba(0,0,0,0.8)' : 'black', // pressed color here

                },
              ]}></Pressable>
          ))}
      </View>
    </View>
  );
};

export default PianoKeys;
