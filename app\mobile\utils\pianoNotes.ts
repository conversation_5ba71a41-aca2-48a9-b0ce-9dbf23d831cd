import Sound from 'react-native-sound';

const getNoteSound = () => {
  const path = '../assets/pianoNotes/';

  const beats: Record<string, number> = {
    '3/4': 3,
    '4/4': 4,
  };

  const soundMap: Record<string, any> = {
    // Octave 1
    C1: require(path + 'C1.mp3'),
    Cs1: require(path + 'Db1.mp3'),
    D1: require(path + 'D1.mp3'),
    Ds1: require(path + 'Eb1.mp3'),
    E1: require(path + 'E1.mp3'),
    F1: require(path + 'F1.mp3'),
    Fs1: require(path + 'Gb1.mp3'),
    G1: require(path + 'G1.mp3'),
    Gs1: require(path + 'Ab1.mp3'),
    A1: require(path + 'A1.mp3'),
    As1: require(path + 'Bb1.mp3'),
    B1: require(path + 'B1.mp3'),

    // Octave 2
    C2: require(path + 'C2.mp3'),
    Cs2: require(path + 'Db2.mp3'),
    D2: require(path + 'D2.mp3'),
    Ds2: require(path + 'Eb2.mp3'),
    E2: require(path + 'E2.mp3'),
    F2: require(path + 'F2.mp3'),
    Fs2: require(path + 'Gb2.mp3'),
    G2: require(path + 'G2.mp3'),
    Gs2: require(path + 'Ab2.mp3'),
    A2: require(path + 'A2.mp3'),
    As2: require(path + 'Bb2.mp3'),
    B2: require(path + 'B2.mp3'),

    // Octave 3
    C3: require(path + 'C3.mp3'),
    Cs3: require(path + 'Db3.mp3'),
    D3: require(path + 'D3.mp3'),
    Ds3: require(path + 'Eb3.mp3'),
    E3: require(path + 'E3.mp3'),
    F3: require(path + 'F3.mp3'),
    Fs3: require(path + 'Gb3.mp3'),
    G3: require(path + 'G3.mp3'),
    Gs3: require(path + 'Ab3.mp3'),
    A3: require(path + 'A3.mp3'),
    As3: require(path + 'Bb3.mp3'),
    B3: require(path + 'B3.mp3'),

    // Octave 4
    C4: require(path + 'C4.mp3'),
    Cs4: require(path + 'Db4.mp3'),
    D4: require(path + 'D4.mp3'),
    Ds4: require(path + 'Eb4.mp3'),
    E4: require(path + 'E4.mp3'),
    F4: require(path + 'F4.mp3'),
    Fs4: require(path + 'Gb4.mp3'),
    G4: require(path + 'G4.mp3'),
    Gs4: require(path + 'Ab4.mp3'),
    A4: require(path + 'A4.mp3'),
    As4: require(path + 'Bb4.mp3'),
    B4: require(path + 'B4.mp3'),

    // Octave 5
    C5: require(path + 'C5.mp3'),
    Cs5: require(path + 'Db5.mp3'),
    D5: require(path + 'D5.mp3'),
    Ds5: require(path + 'Eb5.mp3'),
    E5: require(path + 'E5.mp3'),
    F5: require(path + 'F5.mp3'),
    Fs5: require(path + 'Gb5.mp3'),
    G5: require(path + 'G5.mp3'),
    Gs5: require(path + 'Ab5.mp3'),
    A5: require(path + 'A5.mp3'),
    As5: require(path + 'Bb5.mp3'),
    B5: require(path + 'B5.mp3'),

    // Octave 6
    C6: require(path + 'C6.mp3'),
    Cs6: require(path + 'Db6.mp3'),
    D6: require(path + 'D6.mp3'),
    Ds6: require(path + 'Eb6.mp3'),
    E6: require(path + 'E6.mp3'),
    F6: require(path + 'F6.mp3'),
    Fs6: require(path + 'Gb6.mp3'),
    G6: require(path + 'G6.mp3'),
    Gs6: require(path + 'Ab6.mp3'),
    A6: require(path + 'A6.mp3'),
    As6: require(path + 'Bb6.mp3'),
    B6: require(path + 'B6.mp3'),
  };

  return {soundMap, beats};
};

export default getNoteSound;
