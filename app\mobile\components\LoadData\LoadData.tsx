import {useEffect} from 'react';
import {useSorting} from '../SortingContextProvider/SortingContextProvider';
import {initialData, openLessonDatabase} from '../../utils/databaseSetup';
import AuthService from '../../services/authService';
import {initTts, setSpeakRate} from '../../services/ttsService';
import {
  updatePhraseTable,
  fetchPhrases,
  openPhrasesDatabase,
} from '../../services/phraseManageService';
import {
  fetchMelodies,
  openMelodyDatabase,
} from '../../services/melodyManageService';
import {
  fetchExercises,
  openExerciseDatabase,
} from '../../services/exerciseManageService';
import {
  fetchLessonCount,
  openGraphDatabase,
} from '../../services/graphManageService';
import {fetchLesson} from '../../services/lessonManageService';

const LoadData = ({children}: {children: React.ReactNode}) => {
  const {
    setToken,
    setPhrases,
    sortOption,
    playbackSpeed,
    setMelodies,
    currentLesson,
    setExercises,
    setLessons,
    setLessonCount,
  } = useSorting();
  useEffect(() => {
    initTts();
    setSpeakRate(playbackSpeed);
    (async () => {
      await initialData();
      await AuthService().getAccessToken(setToken);

      const p_db = await openPhrasesDatabase();
      await updatePhraseTable(p_db);
      await fetchPhrases(p_db, setPhrases, sortOption);
      const m_db = await openMelodyDatabase();
      await fetchMelodies(m_db, setMelodies);

      const l_db = await openLessonDatabase();
      const g_db = await openGraphDatabase();
      await fetchLesson(l_db, setLessons);
      await fetchLessonCount(g_db, setLessonCount);

      const e_db = await openExerciseDatabase();
      await fetchExercises(e_db, currentLesson.id, setExercises);
      console.log(p_db);
      console.log('phrases fetched');
    })();
  }, []);

  useEffect(() => {
    playbackSpeed && setSpeakRate(playbackSpeed);
  }, [playbackSpeed]);

  return <>{children}</>;
};

export default LoadData;
