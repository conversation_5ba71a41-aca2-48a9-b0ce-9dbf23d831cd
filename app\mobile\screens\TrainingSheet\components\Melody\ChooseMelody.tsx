import {
  Animated,
  ScrollView,
  Text,
  TouchableOpacity,
  TouchableWithoutFeedback,
  View,
} from 'react-native';
import {Button, Divider} from 'react-native-paper';
import ChooseMelodyStyle from '../../styles/Melody/ChooseMelodyStyle';
import useChooseMelody from '../../hooks/useChooseMelody';

const ChooseMelody = ({hide, navigation}: any) => {
  const styles = ChooseMelodyStyle();

  const {slideAnim, melodies} = useChooseMelody();

  return (
    <TouchableOpacity style={styles.cancel} onPress={hide}>
      <TouchableWithoutFeedback onPress={() => {}}>
        <Animated.View
          style={[styles.container, {transform: [{translateY: slideAnim}]}]}>
          <ScrollView style={styles.scrollView}>
            {melodies.map((melody, index) => {
              return (
                <View key={index} style={styles.item}>
                  <Button>
                    <Text style={styles.optionText}>{melody.name}</Text>
                  </Button>
                </View>
              );
            })}
          </ScrollView>
          <Divider />
          <Button
            onPress={() => {
              hide();
              navigation.navigate('CreateMelody');
            }}>
            Create Melody +
          </Button>
        </Animated.View>
      </TouchableWithoutFeedback>
    </TouchableOpacity>
  );
};

export default ChooseMelody;
