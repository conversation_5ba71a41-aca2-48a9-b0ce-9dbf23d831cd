import {createNativeStackNavigator} from '@react-navigation/native-stack';
import * as React from 'react';
import AnalysisScreen from '../Analysis/AnalysisScreen';
import ExerciseDetail from '../TrainingSheet/ExcerciseDetail';
import ExerciseSheet from '../TrainingSheet/ExerciseSheet';
import LessonPage from '../TrainingSheet/LessonPage';
import CreateMelody from '../TrainingSheet/components/Melody/CreateMelody';

export type LessonStackParamList = {
  LessonPage: undefined;
  ExerciseSheet: undefined;
  ExerciseDetail: undefined;
  AnalysisScreen: undefined;
  CreateMelody: undefined;
};

const Stack = createNativeStackNavigator<LessonStackParamList>();

export const LessonNavigator = () => {
  return (
    <Stack.Navigator initialRouteName="LessonPage">
      <Stack.Screen
        name="LessonPage"
        component={LessonPage}
        options={{headerShown: false}}
      />
      <Stack.Screen
        name="ExerciseSheet"
        component={ExerciseSheet}
        options={{headerShown: false}}
      />
      <Stack.Screen
        name="ExerciseDetail"
        component={ExerciseDetail}
        options={{headerShown: false}}
      />
      <Stack.Screen
        name="AnalysisScreen"
        component={AnalysisScreen}
        options={{headerShown: false}}
      />
      <Stack.Screen
        name="CreateMelody"
        component={CreateMelody}
        options={{headerShown: false}}
      />
    </Stack.Navigator>
  );
};
