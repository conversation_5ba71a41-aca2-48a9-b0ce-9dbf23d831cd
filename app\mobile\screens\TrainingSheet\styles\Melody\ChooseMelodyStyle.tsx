import {StyleSheet} from 'nativewind';
import {useTheme} from 'react-native-paper';

const ChooseMelodyStyle = () => {
  const theme = useTheme();
  return StyleSheet.create({
    cancel: {
      height: '100%',
      width: '100%',
      position: 'relative',
      backgroundColor: 'rgba(0, 0, 0, 0.7)',
    },
    container: {
      position: 'absolute',
      bottom: 0,
      left: 0,
      right: 0,
      backgroundColor: theme.colors.background,
      borderRadius: 20,
      height: '40%',
      paddingHorizontal: 30,
      paddingBottom: '10%',
      paddingTop: 20,
    },
    scrollView: {
      display: 'flex',
      flexDirection: 'column',
    },
    item: {
      width: '100%',
      padding: 4,
      borderRadius: 25,
      marginBottom: 10,
      backgroundColor: theme.colors.background,
    },
    optionText: {
      fontSize: 16,
      fontWeight: 'bold',
      color: '#000',
    },
  });
};

export default ChooseMelodyStyle;
