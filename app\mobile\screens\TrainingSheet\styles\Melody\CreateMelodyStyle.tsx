import {Dimensions, StyleSheet} from 'react-native';
import {useTheme} from 'react-native-paper';

const CreateMelodyStyle = () => {
  const theme = useTheme();

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      flexDirection: 'column',
      paddingHorizontal: 10,
      maxHeight: '100%',
      backgroundColor: theme.colors.background,
      justifyContent: 'space-between',
    },
    button: {
      borderColor: '#A0A4A8',
    },
    mainContent: {
      flex: 1,
    },
    slider: {
      width: '50%',
    },
    sliderBar: {
      width: '100%',
    },
    item: {
      flex: 1,
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      maxHeight: '10%',
    },
    tempoItem: {
      flex: 1,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      maxHeight: '10%',
    },
    label: {
      fontSize: 16,
      fontWeight: 'bold',
      color: '#000',
    },
    row: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    column: {
      flexDirection: 'column',
      alignItems: 'center',
      gap: 8,
    },
    chip: {
      width: 50,
      height: 50,
      borderRadius: 16,
      justifyContent: 'center',
      alignItems: 'center',
      borderWidth: 1.5,
    },

    tempoText: {
      width: 70,
      right: 0,
      top: 15,
      fontSize: 16,
      position: 'absolute',
    },
    noteList: {
      display: 'flex',
      flexDirection: 'row',
    },
    noteRow: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      justifyContent: 'flex-start',
      rowGap: 8,
      paddingVertical: 8,
    },
    noteButton: {
      width: 50,
      height: 50,
      borderRadius: 16,
      borderWidth: 1.5,
      justifyContent: 'center',
      alignItems: 'center',
      padding: 0,
      // marginRight: 8,
    },
    noteButtonContent: {
      width: 50,
      height: 50,
      justifyContent: 'center',
      alignItems: 'center',
      padding: 0,
    },
    piano: {
      backgroundColor: theme.colors.background,
      justifyContent: 'flex-end',
      alignItems: 'flex-end',
    },
    modal: {
      flex: 1,
      alignItems: 'center',
      justifyContent: 'center',
    },
    modalItem: {
      zIndex: 10,
      elevation: 10,
      backgroundColor: 'white',
      paddingVertical: 30,
      paddingHorizontal: 20,
      width: 300,
      borderRadius: 12,
      shadowColor: 'transparent',
      shadowOffset: {width: 0, height: 0},
      shadowOpacity: 0,
      shadowRadius: 0,
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
    },
    modalButton: {
      width: '80%',
    },
    editModalButtons: {
      width: '45%',
    },
  });

  return {
    styles,
  };
};

export default CreateMelodyStyle;
