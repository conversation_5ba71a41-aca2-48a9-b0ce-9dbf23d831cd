import {Dimensions, StyleSheet} from 'react-native';
import {useTheme} from 'react-native-paper';

const PianoKeysStyle = () => {
  const theme = useTheme();

  const WHITE_KEY_WIDTH = 50; // or dynamic if you want
  const BLACK_KEY_WIDTH = WHITE_KEY_WIDTH * 0.66;

  const styles = StyleSheet.create({
    container: {
      width: WHITE_KEY_WIDTH * 7,
      height: 150,
      position: 'relative',
    },
    whiteKeys: {
      flexDirection: 'row',
    },
    whiteKey: {
      width: WHITE_KEY_WIDTH,
      height: '100%',
      backgroundColor: 'rgba(0,0,0,0.02)',
      borderWidth: 3,
      borderTopWidth: 0,
      borderColor: theme.colors.background,
      justifyContent: 'flex-end',
      alignItems: 'center',
      zIndex: 1,
    },
    blackKey: {
      position: 'absolute',
      width: BLACK_KEY_WIDTH,
      height: '60%',
      backgroundColor: 'black',
      zIndex: 2,
    },
    label: {
      fontSize: 12,
      marginBottom: 5,
      color: '#444',
    },
  });

  return {styles, WHITE_KEY_WIDTH};
};
export default PianoKeysStyle;
