import { StyleSheet } from 'nativewind';
import { useTheme } from 'react-native-paper';

const useVoiceEvaluationStyle = () => {
  const theme = useTheme();
  return StyleSheet.create({
    mainContainer: {
      height: '100%',
      width: '100%',
      backgroundColor: theme.colors.background,
    },
    ScrollViewStyled: {
      width: '100%',
      paddingTop: 10,
      position: 'relative',
      backgroundColor: theme.colors.background,
    },
    SpeakIconStyled: {
      height: '12%',
      display: 'flex',
      alignItems: 'center',
      backgroundColor: 'transparent',
      position: 'absolute',
      bottom: 0,
      right: '6%',
    },
    sectionContainer: {
      marginBottom: 0,
    },
    sectionTitle: {
      color: '#4A90E2',
      fontWeight: 'bold',
      paddingLeft: 30,
      paddingVertical: 20,
      fontSize: 16,
    },
    sectionDivider: {
      width: '120%',
      height: 1,
      backgroundColor: '#ccc',
      marginHorizontal: 10,
      marginLeft: '-5%',
      marginTop: -10,
      marginBottom: 0,
    },
  });
};

export default useVoiceEvaluationStyle;
