import {useEffect, useRef} from 'react';
import {Animated} from 'react-native';
import {useSorting} from '../../../components/SortingContextProvider/SortingContextProvider';

const useChooseMelody = () => {
  const {melodies} = useSorting();
  const slideAnim = useRef(new Animated.Value(300)).current; // Start below screen

  useEffect(() => {
    Animated.timing(slideAnim, {
      toValue: 0,
      duration: 100,
      useNativeDriver: true,
    }).start();
  }, []);

  return {
    melodies,
    slideAnim,
  };
};

export default useChooseMelody;
