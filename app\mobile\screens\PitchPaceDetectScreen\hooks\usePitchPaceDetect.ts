import {useSorting} from '../../../components/SortingContextProvider/SortingContextProvider';
import {useEffect, useState, useRef, useCallback} from 'react';
import {VoiceProcessor} from '@picovoice/react-native-voice-processor';
import {Alert, PermissionsAndroid, Platform} from 'react-native';
import {useFocusEffect} from '@react-navigation/native';
import PitchFinder from 'pitchfinder';

const hzToNote = (freq: number): string | null => {
  if (freq <= 0 || isNaN(freq) || !isFinite(freq) || freq < 50 || freq > 2000) {
    return null;
  }

  const midiNumber = Math.round(69 + 12 * Math.log2(freq / 440.0));

  if (midiNumber < 0 || midiNumber > 127) {
    return null;
  }

  const noteNames = ['C', 'C#', 'D', 'D#', 'E', 'F', 'F#', 'G', 'G#', 'A', 'A#', 'B'];
  const noteIndex = midiNumber % 12;
  const octave = Math.floor(midiNumber / 12) - 1;

  return `${noteNames[noteIndex]}${octave}`;
};

export const usePitchPaceDetect = () => {
  const [showVolume, setShowVolume] = useState<boolean>(true);
  const [showPitch, setShowPitch] = useState<boolean>(true);
  const [isRecording, setIsRecording] = useState<boolean>(false);
  const [pitch, setPitch] = useState<number | null>(0);
  const [volume, setVolume] = useState<number>(0);
  const [isSpeaking, setIsSpeaking] = useState<boolean>(false);
  const [musicalNote, setMusicalNote] = useState<string | null>(null);

  const rawPitchRef = useRef<number | null>(0);
  const rawVolumeRef = useRef<number>(0);
  const updateIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const speakingRef = useRef<boolean>(false);
  const showPitchRef = useRef<boolean>(showPitch);
  const showVolumeRef = useRef<boolean>(showVolume);
  const float32FrameRef = useRef<Float32Array | null>(null);

  const pitchBufferRef = useRef<number[]>([]);
  const volumeBufferRef = useRef<number[]>([]);
  const windowSize = 5;

  let voiceProcessor = VoiceProcessor.instance;

  const pitchDetector = useRef(
    PitchFinder.YIN({ sampleRate: 16000 })
  ).current;

  useSorting();

  useEffect(() => {
    showPitchRef.current = showPitch;
  }, [showPitch]);

  useEffect(() => {
    showVolumeRef.current = showVolume;
  }, [showVolume]);

  const getAverage = (arr: number[]) =>
    arr.length === 0 ? 0 : arr.reduce((a, b) => a + b, 0) / arr.length;

  const hasChanged = (a: number | null, b: number | null, threshold = 0.5) =>
    Math.abs(((a ?? 0) - (b ?? 0))) > threshold;

  const calculateDbFS = (
    samples: number[],
    maxAmplitude = 32768,
    minDb = -65,
  ) => {
    if (!samples.length) return -Infinity;
    const sumSquares = samples.reduce((sum, s) => sum + s * s, 0);
    const rms = Math.sqrt(sumSquares / samples.length);
    const dbfs = 20 * Math.log10(rms / maxAmplitude);
    const clamped = Math.max(dbfs, minDb);
    const normalized = (clamped - minDb) / -minDb;
    return Math.round(Math.pow(normalized, 0.5) * 100);
  };

  const startRecording = async () => {
    const frameLength = 1024;
    const sampleRate = 16000;
    if (await voiceProcessor.hasRecordAudioPermission()) {
      await voiceProcessor.start(frameLength, sampleRate);
    }
  };

  const stopRecording = async () => {
    try {
      await voiceProcessor.stop();
    } catch (e) {
      console.log("Error stopping recording: ", e)
    }
  };

  useEffect(() => {
    if (Platform.OS !== 'android') return;
    PermissionsAndroid.request(PermissionsAndroid.PERMISSIONS.RECORD_AUDIO, {
      title: 'Microphone Permission',
      message: 'App needs access to your microphone',
      buttonNeutral: 'Ask Me Later',
      buttonNegative: 'Cancel',
      buttonPositive: 'OK',
    })
      .then(granted => {
        if (granted !== PermissionsAndroid.RESULTS.GRANTED) {
          Alert.alert('Permission Denied', 'Microphone permission is required');
        }
      })
      .catch(err => console.warn('Permission error:', err));
  }, []);

  const setupAudioProcessing = useCallback(() => {
    voiceProcessor.addFrameListener((frame: number[]) => {
      const db = calculateDbFS(frame);
      const volBuf = volumeBufferRef.current;
      volBuf.push(db);
      if (volBuf.length > windowSize) volBuf.shift();
      rawVolumeRef.current = getAverage(volBuf);

      if (showPitchRef.current) {
        if (!float32FrameRef.current || float32FrameRef.current.length !== frame.length) {
          float32FrameRef.current = new Float32Array(frame.length);
        }

        for (let i = 0; i < frame.length; i++) {
          float32FrameRef.current[i] = frame[i] / 32768;
        }

        const detectedPitch = pitchDetector(float32FrameRef.current);

        if (detectedPitch && detectedPitch >= 50 && detectedPitch <= 1000) {
          const buf = pitchBufferRef.current;
          buf.push(detectedPitch);
          if (buf.length > windowSize) buf.shift();
          rawPitchRef.current = getAverage(buf);

          if (!speakingRef.current) {
            speakingRef.current = true;
            setIsSpeaking(true);
          }
        } else if (speakingRef.current) {
          speakingRef.current = false;
          setIsSpeaking(false);
        }
      }
    });

    updateIntervalRef.current = setInterval(() => {
      const currentPitch = rawPitchRef.current;
      const currentVolume = rawVolumeRef.current;

      setPitch(prev => hasChanged(prev, currentPitch) ? currentPitch : prev);
      setVolume(prev => hasChanged(prev, currentVolume) ? currentVolume : prev);

      if (currentPitch && currentPitch > 0) {
        const currentNote = hzToNote(currentPitch);
        setMusicalNote(prev => prev !== currentNote ? currentNote : prev);
      } else {
        setMusicalNote(null);
      }
    }, 100);
  }, []);

  const cleanupAudioProcessing = useCallback(() => {
    stopRecording();
    voiceProcessor.clearFrameListeners();

    if (updateIntervalRef.current) {
      clearInterval(updateIntervalRef.current);
      updateIntervalRef.current = null;
    }
  }, []);

  useFocusEffect(
    useCallback(() => {
      setupAudioProcessing();
      startRecording();
      setIsRecording(true);

      return () => {
        cleanupAudioProcessing();
        setIsRecording(false);
      };
    }, [setupAudioProcessing, cleanupAudioProcessing]),
  );

  return {
    showPitch,
    setShowPitch,
    pitch,
    showVolume,
    setShowVolume,
    volume,
    isRecording,
    isSpeaking,
    rawPitchRef,
    rawVolumeRef,
    musicalNote,
    hzToNote,
  };
};
