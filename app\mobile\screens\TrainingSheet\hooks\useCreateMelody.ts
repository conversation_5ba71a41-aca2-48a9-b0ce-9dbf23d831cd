import {useLessonNavigation} from '../../../utils/useAppNavigation';
import getNoteSound from '../../../utils/pianoNotes';
import {
  insertMelody,
  Melody,
  openMelodyDatabase,
  scaleDirectionTypes,
} from '../../../services/melodyManageService';
import {useSorting} from '../../../components/SortingContextProvider/SortingContextProvider';
import {useTheme} from 'react-native-paper';
import {useReducer, useState} from 'react';

type MelodyState = {
  name: string;
  tempo: number;
  scaleDirection: scaleDirectionTypes;
  beat: string;
  notes: string[];
  selectedNote: number;
  chordNotes: string[];
  selectedChordNote: number;
  changingChord: boolean;
};

type MelodyAction =
  | {type: 'SET_NAME'; payload: string}
  | {type: 'SET_TEMPO'; payload: number}
  | {type: 'SET_SCALE_DIRECTION'; payload: scaleDirectionTypes}
  | {type: 'SET_BEAT'; payload: string}
  | {type: 'SET_SELECTED_NOTE'; payload: number}
  | {type: 'SET_SELECTED_CHORD_NOTE'; payload: number}
  | {type: 'SET_CHANGING_CHORD'; payload: boolean}
  | {type: 'ADD_NOTE'; payload: string}
  | {type: 'UPDATE_NOTE'; index: number; newNote: string}
  | {type: 'DELETE_NOTE'; index: number}
  | {type: 'UPDATE_CHORD_NOTE'; index: number; newNote: string}
  | {type: 'CLEAR_CHORD_NOTE'}
  | {type: 'CLEAR_NOTE'};

const initialState: MelodyState = {
  name: 'Placeholder',
  tempo: 90,
  scaleDirection: 'ascending',
  beat: '3/4',
  notes: [],
  selectedNote: -1,
  chordNotes: ['', '', ''],
  selectedChordNote: -1,
  changingChord: false,
};

const melodyReducer = (
  state: MelodyState,
  action: MelodyAction,
): MelodyState => {
  switch (action.type) {
    case 'SET_NAME':
      return {...state, name: action.payload};
    case 'SET_TEMPO':
      return {...state, tempo: action.payload};
    case 'SET_SCALE_DIRECTION':
      return {...state, scaleDirection: action.payload};
    case 'SET_BEAT':
      return {...state, beat: action.payload};
    case 'SET_SELECTED_NOTE':
      return {...state, selectedNote: action.payload};
    case 'SET_SELECTED_CHORD_NOTE':
      return {...state, selectedChordNote: action.payload};
    case 'SET_CHANGING_CHORD':
      return {...state, changingChord: action.payload};
    case 'ADD_NOTE':
      return {...state, notes: [...state.notes, action.payload]};
    case 'UPDATE_NOTE': {
      const updatedNotes = [...state.notes];
      updatedNotes[action.index] = action.newNote;
      return {...state, notes: updatedNotes};
    }
    case 'DELETE_NOTE': {
      const newNotes =
        action.index > -1
          ? state.notes.filter((_, i) => i !== action.index)
          : state.notes.slice(0, -1);

      let newSelected = state.selectedNote;

      //Keep selected note in array after deletion
      if (newNotes.length === 0) {
        newSelected = -1;
      } else if (state.selectedNote !== -1) {
        if (action.index === state.selectedNote) {
          newSelected = Math.max(0, state.selectedNote - 1);
        } else if (state.selectedNote >= newNotes.length) {
          newSelected = newNotes.length - 1;
        }
      }
      return {
        ...state,
        notes: newNotes,
        selectedNote: newSelected,
      };
    }
    case 'UPDATE_CHORD_NOTE': {
      const updatedChordNotes = [...state.chordNotes];
      updatedChordNotes[action.index] = action.newNote;
      return {...state, chordNotes: updatedChordNotes};
    }
    case 'CLEAR_CHORD_NOTE': {
      return {...state, chordNotes: ['', '', '']};
    }
    case 'CLEAR_NOTE': {
      return {...state, notes: []};
    }
    default:
      return state;
  }
};

const useCreateMelody = () => {
  const {setMelodies} = useSorting();

  const navigation = useLessonNavigation();

  const [melody, dispatch] = useReducer(melodyReducer, initialState);
  const [showModal, setShowModal] = useState(false);
  const [modalMode, setModalMode] = useState<'beat' | 'scale'>('beat');

  const [titleName, setTitleName] = useState('');
  const [editName, setEditName] = useState(false);

  const {beats} = getNoteSound();

  const pianoRange = [1, 2, 3, 4, 5, 6];

  const beatRange = ['3/4', '4/4'];

  const scaleDirections: scaleDirectionTypes[] = [
    'ascending',
    'descending',
    'none',
  ];

  const changeChord = (index: number) => {
    dispatch({type: 'SET_SELECTED_CHORD_NOTE', payload: index});
    dispatch({type: 'SET_CHANGING_CHORD', payload: true});
  };

  const changeNote = (index: number) => {
    dispatch({type: 'SET_SELECTED_NOTE', payload: index});
    dispatch({type: 'SET_CHANGING_CHORD', payload: false});
  };

  const addNote = (newNote: string) => {
    if (melody.changingChord) {
      dispatch({
        type: 'UPDATE_CHORD_NOTE',
        index: melody.selectedChordNote,
        newNote,
      });
      if (melody.selectedChordNote < melody.chordNotes.length - 1) {
        dispatch({
          type: 'SET_SELECTED_CHORD_NOTE',
          payload: melody.selectedChordNote + 1,
        });
      }
    } else if (melody.selectedNote === -1) {
      dispatch({type: 'ADD_NOTE', payload: newNote});
    } else {
      dispatch({type: 'UPDATE_NOTE', index: melody.selectedNote, newNote});
      if (melody.selectedNote + 1 === melody.notes.length) {
        dispatch({type: 'SET_SELECTED_NOTE', payload: -1});
      } else {
        dispatch({type: 'SET_SELECTED_NOTE', payload: melody.selectedNote + 1});
      }
    }
  };

  const setBeat = (beat: string) => {
    dispatch({type: 'SET_BEAT', payload: beat});
    setShowModal(false);
  };

  const setScale = (scale: scaleDirectionTypes) => {
    dispatch({type: 'SET_SCALE_DIRECTION', payload: scale});
    setShowModal(false);
  };

  const changeBeat = () => {
    setModalMode('beat');
    setShowModal(true);
  };

  const changeScale = () => {
    setModalMode('scale');
    setShowModal(true);
  };

  const saveMelody = async () => {
    const melodyItem: Melody = {
      name: melody.name,
      tempo: melody.tempo,
      beat: melody.beat,
      chord: melody.chordNotes,
      scale: melody.scaleDirection,
      notes: melody.notes,
    };
    const db = await openMelodyDatabase();
    await insertMelody(db, melodyItem, setMelodies);
    await db.close();
  };

  return {
    navigation,
    melody,
    dispatch,
    showModal,
    setShowModal,
    modalMode,
    setModalMode,
    titleName,
    setTitleName,
    editName,
    setEditName,
    beats,
    pianoRange,
    beatRange,
    scaleDirections,
    changeChord,
    changeNote,
    addNote,
    setBeat,
    setScale,
    changeBeat,
    changeScale,
    saveMelody,
  };
};

export default useCreateMelody;
