import React, { FC, useEffect, useRef, useMemo } from 'react';
import { View, Text } from 'react-native';
import { useTheme } from 'react-native-paper';
import Svg, { Path, Rect, Line, G, Text as SvgText } from 'react-native-svg';
import { PitchPaceDetectStyle, useChartConstants } from '../styles/PitchPaceDetectStyle';

interface RealTimeChartProps {
  volumeData: number;
  pitchData: number | null;
  isSpeaking: boolean;
  showVolume: boolean;
  showPitch: boolean;
}

const SvgChart: FC<RealTimeChartProps> = ({
  volumeData,
  pitchData,
  showVolume,
  showPitch,
}) => {
  const theme = useTheme();
  const styles = PitchPaceDetectStyle();
  const constants = useChartConstants();

  const dataBufferRef = useRef({
    volume: Array(constants.bufferSize).fill(10),
    pitch: Array(constants.bufferSize).fill(null),
    lastPitch: null as number | null,
  });

  const repeatRef = useRef(0);

  const chartWidth = constants.width;
  const chartHeight = constants.height;
  const padding = constants.padding;
  const plotWidth = chartWidth - padding.left - padding.right;
  const plotHeight = chartHeight - padding.top - padding.bottom;

  useEffect(() => {
    const validPitch =
      pitchData !== null && pitchData > 80 && pitchData < 1000
        ? pitchData
        : null;
    const validVolume =
      volumeData >= 0 && volumeData < 200
        ? volumeData
        : dataBufferRef.current.volume[constants.bufferSize - 1] || 10;

    for (let i = 0; i < constants.bufferSize - 1; i++) {
      dataBufferRef.current.volume[i] = dataBufferRef.current.volume[i + 1];
      dataBufferRef.current.pitch[i] = dataBufferRef.current.pitch[i + 1];
    }

    dataBufferRef.current.volume[constants.bufferSize - 1] = validVolume;

    if (validPitch === dataBufferRef.current.lastPitch) {
      repeatRef.current++;
    } else {
      repeatRef.current = 0;
      dataBufferRef.current.lastPitch = validPitch;
    }

    const finalPitch = repeatRef.current > 4 ? null : validPitch;
    dataBufferRef.current.pitch[constants.bufferSize - 1] = finalPitch;
  }, [volumeData, pitchData, constants.bufferSize]);

  const scaleX = (index: number) => padding.left + (index / (constants.bufferSize - 1)) * plotWidth;
  const scalePitchY = (value: number) => padding.top + plotHeight - ((value - 50) / (550 - 50)) * plotHeight;
  const scaleVolumeY = (value: number) => padding.top + plotHeight - (value / 100) * plotHeight;

  const pitchPaths = useMemo(() => {
    if (!showPitch) return [];

    const segments: string[] = [];
    let currentSegment = '';
    let inSegment = false;

    dataBufferRef.current.pitch.forEach((value, index) => {
      const x = scaleX(index);

      if (value !== null && value > 0) {
        const y = scalePitchY(value);

        if (!inSegment) {
          currentSegment = `M ${x} ${y}`;
          inSegment = true;
        } else {
          currentSegment += ` L ${x} ${y}`;
        }
      } else {
        if (inSegment && currentSegment) {
          segments.push(currentSegment);
          currentSegment = '';
          inSegment = false;
        }
      }
    });

    if (inSegment && currentSegment) {
      segments.push(currentSegment);
    }

    return segments;
  }, [showPitch, pitchData, scaleX, scalePitchY]);

  const volumePath = useMemo(() => {
    if (!showVolume) return '';

    let pathData = '';
    let hasStarted = false;

    dataBufferRef.current.volume.forEach((value, index) => {
      if (value !== null && value >= 0) {
        const x = scaleX(index);
        const y = scaleVolumeY(value);

        if (!hasStarted) {
          pathData += `M ${x} ${y}`;
          hasStarted = true;
        } else {
          pathData += ` L ${x} ${y}`;
        }
      }
    });

    return pathData;
  }, [showVolume, volumeData, scaleX, scaleVolumeY]);

  const maleRangeTop = scalePitchY(155);
  const maleRangeBottom = scalePitchY(85);
  const femaleRangeTop = scalePitchY(255);
  const femaleRangeBottom = scalePitchY(165);

  const gridElements = useMemo(() => {
    const elements: Array<{
      type: 'line';
      x1: number;
      y1: number;
      x2: number;
      y2: number;
      key: string;
      stroke: string;
      strokeWidth: number;
    } | {
      type: 'text';
      x: number;
      y: number;
      text: string;
      key: string;
      fill: string;
      fontSize: number;
      textAnchor: 'start' | 'middle' | 'end';
    }> = [];

    constants.pitchTicks.forEach(hz => {
      const y = scalePitchY(hz);
      elements.push({
        type: 'line',
        x1: padding.left,
        y1: y,
        x2: padding.left + plotWidth,
        y2: y,
        key: `h-${hz}`,
        stroke: constants.colors.gridLine,
        strokeWidth: 1
      });
    });

    for (let i = 0; i <= 5; i++) {
      const x = padding.left + (i / 5) * plotWidth;
      elements.push({
        type: 'line',
        x1: x,
        y1: padding.top,
        x2: x,
        y2: padding.top + plotHeight,
        key: `v-${i}`,
        stroke: constants.colors.gridLineSubtle,
        strokeWidth: 0.5
      });
    }

    constants.pitchTicks.forEach(hz => {
      const y = scalePitchY(hz);
      elements.push({
        type: 'text',
        x: padding.left - 10,
        y: y + 3,
        text: hz.toString(),
        key: `label-pitch-${hz}`,
        fill: constants.colors.textColor,
        fontSize: 10,
        textAnchor: 'end'
      });
    });

    constants.volumeTicks.forEach(vol => {
      const y = scaleVolumeY(vol);
      elements.push({
        type: 'text',
        x: padding.left + plotWidth + 10,
        y: y + 3,
        text: vol.toString(),
        key: `label-volume-${vol}`,
        fill: constants.colors.textColor,
        fontSize: 10,
        textAnchor: 'start'
      });
    });

    return elements;
  }, [plotWidth, plotHeight, padding, scalePitchY, scaleVolumeY, constants]);

  return (
    <View style={styles.svgChartContainer}>
      <View style={styles.chartAxisLabels}>
        <Text style={[styles.chartAxisLabel, { color: constants.colors.pitch }]}>Hz</Text>
        <Text style={[styles.chartAxisLabel, { color: constants.colors.volume }]}>dB</Text>
      </View>

      <Svg width={chartWidth} height={chartHeight} style={styles.chart}>
        <G>
          <Rect
            x={0}
            y={0}
            width={chartWidth}
            height={chartHeight}
            fill={theme.colors.background}
            stroke="none"
          />

          {showPitch && (
            <>
              <Rect
                x={padding.left}
                y={maleRangeTop}
                width={plotWidth}
                height={maleRangeBottom - maleRangeTop}
                fill={constants.colors.maleRange}
              />

              <Rect
                x={padding.left}
                y={femaleRangeTop}
                width={plotWidth}
                height={femaleRangeBottom - femaleRangeTop}
                fill={constants.colors.femaleRange}
              />
            </>
          )}

          {gridElements.map((element) => {
            if (element.type === 'line') {
              return (
                <Line
                  key={element.key}
                  x1={element.x1}
                  y1={element.y1}
                  x2={element.x2}
                  y2={element.y2}
                  stroke={element.stroke}
                  strokeWidth={element.strokeWidth}
                />
              );
            } else if (element.type === 'text') {
              return (
                <SvgText
                  key={element.key}
                  x={element.x}
                  y={element.y}
                  fill={element.fill}
                  fontSize={element.fontSize}
                  textAnchor={element.textAnchor}
                >
                  {element.text}
                </SvgText>
              );
            }
            return null;
          })}

          {showVolume && volumePath && (
            <Path
              d={volumePath}
              stroke={constants.colors.volume}
              strokeWidth={2}
              fill="none"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          )}

          {showPitch && pitchPaths.map((pathData, index) => (
            <Path
              key={`pitch-segment-${index}`}
              d={pathData}
              stroke={constants.colors.pitch}
              strokeWidth={2}
              fill="none"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          ))}
        </G>
      </Svg>
    </View>
  );
};

export default SvgChart;
